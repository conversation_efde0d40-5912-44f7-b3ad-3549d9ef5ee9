<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace app\api\controller;

use app\model\goods\FastGoodsAdd;
use think\facade\Request;
use think\Response;

/**
 * 商品快速导入API
 * 
 * 接口说明：
 * 本接口用于快速导入商品数据，无需登录验证
 * 
 * 请求方式：POST
 * 请求URL：/api/fastimport/add
 */
class FastImport
{
    /**
     * 站点ID
     * @var int
     */
    protected $site_id = 1;
    
    /**
     * 请求参数
     * @var array
     */
    protected $params = [];
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 处理请求参数
        $this->params = Request::param();

        // 获取站点ID
        $this->site_id = isset($this->params['site_id']) ? intval($this->params['site_id']) : 1;
    }

    /**
     * 生成SKU编号
     * @param array $data 商品数据或SKU数据
     * @param string $spuId SPU ID
     * @return string
     */
    private function generateSkuNo($data, $spuId)
    {
        $rand = rand(1000, 9999);
        $hostUpc = isset($data['spuExtDTO']['hostUpc']) ? $data['spuExtDTO']['hostUpc'] : [];
        // 去重
        $hostUpc = array_unique($hostUpc);
        // 第一优先级：检查 hostUpc
        if (!empty($hostUpc)) {
            return implode(',',$hostUpc) . "-" . $spuId . "-" . $rand;
        }

        // 第二优先级：检查 hostItem
        if (!empty($data['hostItem'])) {
            return $data['hostItem'] . "-" . $spuId . "-" . $rand;
        }

        // 默认值：生成格式 SKU + 时间戳 + spuId
        return "SKU" . date('YmdHis') . "-" . $spuId . "-" . $rand;
    }
    
    /**
     * 处理外部商品数据并添加到系统
     * @param array $directData 直接传入的数据（可选）
     * @return mixed
     */
    public function add($directData = null)
    {
        // 确定要处理的数据，优先使用直接传入的数据
        if ($directData !== null) {
            // 使用直接传入的数据
            $jsonData = $directData;
        } else {
            // 如果POST中有json字段，尝试解析它
            if (isset($this->params['json'])) {
                $jsonData = json_decode($this->params['json'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->response([
                        'code' => -1,
                        'message' => 'JSON解析错误: ' . json_last_error_msg()
                    ]);
                }
            } else {
                // 直接使用POST参数
                $jsonData = $this->params;
            }
            
            // 检查数据是否为空
            if (empty($jsonData)) {
                return $this->response([
                    'code' => -1,
                    'message' => '没有接收到有效的数据'
                ]);
            }
        }
        
        // 处理商品数据
        $goodsData = $this->processData($jsonData);
        
        // 如果处理过程中已经返回错误，直接返回
        if (isset($goodsData['code']) && $goodsData['code'] !== 0) {
            return $this->response($goodsData);
        }
        
        // 调用FastGoodsAdd模型添加商品
        $fastGoods = new FastGoodsAdd();
        $result = $fastGoods->addGoods($goodsData);
        
        return $this->response($result);
    }
    
    /**
     * 根据标签标题获取对应的标签ID
     * @param string $title 标签标题
     * @return array 包含标签ID和标签名称的数组
     */
    protected function getLabelIdByTitle($title)
    {
        $labelMap = [
            '冷藏' => 1,
            '冷冻' => 2,
            '冰鲜' => 3,
            '有机' => 4,
            '进口' => 5,
            '品质好' => 6,
            '很新鲜' => 7,
            '个头大' => 8,
            '份量大' => 9,
            '分量足' => 10,
            '非自营' => 11,
            '口感Q弹' => 12,
            '做菜好吃' => 13,
            '滋润保湿' => 14,
            '值得推荐' => 15,
            '真材实料' => 16,
            '油香浓郁' => 17,
            '性价比高' => 18,
            '新鲜美味' => 19,
            '新鲜度高' => 20,
            '新旧包装' => 21,
            '香味浓郁' => 22,
            '香味持久' => 23,
            '香甜软糯' => 24,
            '鲜嫩可口' => 25,
            '鲜脆可口' => 26,
            '吸水性好' => 27,
            '吸附力强' => 28,
            '温和保湿' => 29,
            '味道鲜美' => 30,
            '味道清新' => 31,
            '味道浓郁' => 32,
            '味道纯正' => 33,
            '万条好评' => 34,
            '万人回购' => 35,
            '万能酱汁' => 36,
            '甜度适中' => 37,
            '酸甜可口' => 38,
            '睡感舒适' => 39,
            '水分充足' => 40,
            '刷毛柔软' => 41,
            '使用方便' => 42,
            '食用方便' => 43,
            '肉质鲜嫩' => 44,
            '肉质紧实' => 45,
            '肉味浓郁' => 46,
            '肉嫩多汁' => 47,
            '柔软亲肤' => 48,
            '容易冲泡' => 49,
            '日期新鲜' => 50,
            '清新好闻' => 51,
            '清甜可口' => 52,
            '清洁力强' => 53,
            '清洁干净' => 54,
            '轻便灵活' => 55,
            '轻薄透气' => 56,
            '气味清新' => 57,
            '气泡充足' => 58,
            '皮薄馅多' => 59,
            '蓬松柔顺' => 60,
            '烹饪方便' => 61,
            '烹饪必备' => 62,
            '奶味浓郁' => 63,
            '面食筋道' => 64,
            '面料舒适' => 65,
            '美味可口' => 66,
            '留香持久' => 67,
            '量大料足' => 68,
            '口感细腻' => 69,
            '口感酥脆' => 70,
            '口感松软' => 71,
            '口感丝滑' => 72,
            '口感顺滑' => 73,
            '口感爽脆' => 74,
            '口感软糯' => 75,
            '口感清爽' => 76,
            '口感俱佳' => 77,
            '口感劲道' => 78,
            '口感丰富' => 79,
            '口感脆甜' => 80,
            '口感纯正' => 81,
            '颗粒饱满' => 82,
            '开胃零食' => 83,
            '酒香浓郁' => 84,
            '酒味醇正' => 85,
            '解馋必备' => 86,
            '健康美味' => 87,
            '挤压方便' => 88,
            '厚实耐用' => 89,
            '好吃不腻' => 90,
            '果味浓郁' => 91,
            '果仁饱满' => 92,
            '个大流油' => 93,
            '个大饱满' => 94,
            '干净卫生' => 95,
            '方便实用' => 96,
            '方便快捷' => 97,
            '方便服用' => 98,
            '豆香味浓' => 99,
            '灯光柔和' => 100,
            '储存方便' => 101,
            '抽取方便' => 102,
            '冲泡方便' => 103,
            '茶味浓郁' => 104,
            '操作简单' => 105,
            '补水保湿' => 106,
            '冰爽可口' => 107,
            '宝贝爱吃' => 108,
            '宝宝喜欢' => 109,
            '好喝无负担' => 110,
            '可全国配送' => 111,
            '商品品质好' => 112,
            '温和无刺激' => 113,
            '温和不伤手' => 114
        ];
        
        if (isset($labelMap[$title])) {
            return [
                'label_id' => $labelMap[$title],
                'label_name' => $title
            ];
        }
        
        return [
            'label_id' => 0,
            'label_name' => ''
        ];
    }
    
    /**
     * 根据溢价规则计算销售价格和成本价
     * 商品单价＜300，溢价：15%
     * 商品单价≥300，溢价：10%
     * 商品单价≥600，溢价：8%
     * 商品单价≥1000，溢价：5%
     * 
     * @param float $basePrice 基础价格
     * @return array 包含销售价格和成本价的数组
     */
    protected function calculatePrices($basePrice)
    {
        // 成本价就是基础价格
        $costPrice = $basePrice;
        
        // 根据基础价格计算溢价比例
        $markup = 0.15; // 默认溢价15%
        
        if ($basePrice >= 1000) {
            $markup = 0.05; // 溢价5%
        } elseif ($basePrice >= 600) {
            $markup = 0.08; // 溢价8%
        } elseif ($basePrice >= 300) {
            $markup = 0.10; // 溢价10%
        }
        
        // 计算销售价格（基础价格 + 溢价）
        $salePrice = round($basePrice * (1 + $markup), 2);
        
        return [
            'sale_price' => $salePrice,
            'cost_price' => $costPrice
        ];
    }
    
    /**
     * 处理外部商品数据为系统可用格式
     * @param array $data 原始数据
     * @return array 处理后的数据
     */
    protected function processData($data)
    {
        // 检查必要字段
        if (empty($data['title'])) {
            return [
                'code' => -1,
                'message' => '商品名称不能为空'
            ];
        }

        // 处理多张主图
        $goodsImages = [];
        if (!empty($data['images']) && is_array($data['images'])) {
            $goodsImages = $data['images'];
        }
        
        // 确保至少有一张图片
        if (empty($goodsImages)) {
            $goodsImages[] = 'upload/common/default_goods_img.png';
        }
        
        // 获取默认销售价格
        $defaultPrice = 0;
        $defaultMarketPrice = 0;
        $defaultCostPrice = 0;
        
        // 优先使用price字段中的价格
        if (!empty($data['price']) && is_numeric($data['price'])) {
            $basePrice = is_numeric($data['price']) ? floatval($data['price']) : 9999999;
            $basePrice = round($basePrice / 100, 2);
            // 计算销售价格和成本价
            $priceInfo = $this->calculatePrices($basePrice);
            $defaultPrice = $priceInfo['sale_price'];
            $defaultCostPrice = $priceInfo['cost_price'];
            
            // 市场价处理
            if (!empty($data['marketPrice']) && is_numeric($data['marketPrice'])) {
                $marketPrice = floatval($data['marketPrice']);
                if ($marketPrice > 1000) {
                    $marketPrice = round($marketPrice / 100, 2);
                }
                $defaultMarketPrice = $marketPrice;
            } else {
                // 市场价默认为销售价的1.2倍
                $defaultMarketPrice = round($defaultPrice * 1.2, 2);
            }
        }elseif (!empty($data['priceInfo']) && is_array($data['priceInfo'])) {
            // 如果price字段不存在，则从priceInfo中获取
            foreach ($data['priceInfo'] as $price) {
                if (isset($price['priceType']) && $price['priceType'] == 1 && isset($price['price'])) {
                    // 价格可能是字符串格式的数字（例如"784900"），需要转换
                    $basePrice = is_numeric($price['price']) ? floatval($price['price']) : 9999999;
                    // 如果是以分为单位的价格（大于1000），转换为元
                    $basePrice = round($basePrice / 100, 2);
                    
                    // 计算销售价格和成本价
                    $priceInfo = $this->calculatePrices($basePrice);
                    $defaultPrice = $priceInfo['sale_price'];
                    $defaultCostPrice = $priceInfo['cost_price'];
                    
                    // 市场价处理
                    if (isset($price['marketPrice'])) {
                        $marketPrice = is_numeric($price['marketPrice']) ? floatval($price['marketPrice']) : 0;
                        if ($marketPrice > 1000) {
                            $marketPrice = round($marketPrice / 100, 2);
                        }
                        $defaultMarketPrice = $marketPrice;
                    } else {
                        // 市场价默认为销售价的1.2倍
                        $defaultMarketPrice = round($defaultPrice * 1.2, 2);
                    }
                    break;
                }
            }
        }
        
        // 获取默认重量
        $defaultWeight = 0;
        if (!empty($data['weight'])) {
            $defaultWeight = floatval($data['weight']);
        } else if (!empty($data['netWeight'])) {
            $defaultWeight = floatval($data['netWeight']);
        } else if (!empty($data['spuExtDTO']['weight'])) {
            $defaultWeight = floatval($data['spuExtDTO']['weight']);
        } else if (!empty($data['spuExtDTO']['netWeight'])) {
            $defaultWeight = floatval($data['spuExtDTO']['netWeight']);
        }
        $category_id = !empty($data['categoryId']) ? $data['categoryId'] : ''; //',1,2,3,'
        $category_json = !empty($data['category_json']) ? $data['category_json'] : '{}';
        
        // 处理标签信息，获取tagPlace=7的标签
        $label_id = 0;
        $label_name = '';
        if (!empty($data['tagInfo']) && is_array($data['tagInfo'])) {
            foreach ($data['tagInfo'] as $tag) {
                if (isset($tag['tagPlace']) && $tag['tagPlace'] == 7 && !empty($tag['title'])) {
                    $labelInfo = $this->getLabelIdByTitle($tag['title']);
                    $label_id = $labelInfo['label_id'];
                    $label_name = $labelInfo['label_name'];
                    
                    // 找到匹配的标签后跳出循环
                    if ($label_id > 0) break;
                }
            }
        }

        $stock = $data['stock'] ?? 10;
        $smallPackageUnit = !empty($data['spuExtDTO']['smallPackageUnit']) ? $data['spuExtDTO']['smallPackageUnit'] : '';
        // 如果$smallPackageUnit 非中文，默认默认显示空
        if (!preg_match('/^[\x{4e00}-\x{9fa5}]+$/u', $smallPackageUnit)) {
            $smallPackageUnit = '';
        }

        // 构建商品数据
        $goodsData = [
            // 基本信息
            'goods_id' => $data['spuId'],
            'goods_name' => $data['title'],
            'goods_class' => 1, // 实物商品
            'goods_class_name' => '实物商品',
            'goods_attr_class' => 0,
            'goods_attr_name' => '',
            'site_id' => $this->site_id,
            'site_name' => '默认站点',
            
            // 商品ID
            'goods_spu_id' => !empty($data['spuId']) ? $data['spuId'] : '',
            
            // 分类信息 - 使用默认分类或尝试映射分类
            'category_id' => $category_id, // 默认分类
            'category_json' => $category_json,
            
            // 图片信息 - 第一张作为主图
            'goods_image' => $goodsImages[0],
            'goods_more_image' => implode(',', $goodsImages),
            
            // 商品详情
            'goods_content' => !empty($data['desc']) ? $data['desc'] : '<p>商品详情</p>',
            
            // 商品状态
            'goods_state' => 1, // 上架状态
            'verify_state' => 1,
            
            // 价格信息
            'price' => $defaultPrice,
            'market_price' => $defaultMarketPrice,
            'cost_price' => $defaultCostPrice,
            
            // 库存信息
            'stock' => $stock,
            'goods_stock' => $stock,
            'goods_stock_alarm' => !empty($data['stockInfo']['safeStockQuantity']) ? $data['stockInfo']['safeStockQuantity'] : 10,
            
            // 重量信息
            'weight' => $defaultWeight,
            
            // 其他属性
            'is_virtual' => 0, // 非虚拟商品
            'virtual_indate' => 0,
            'is_free_shipping' => 1, // 免邮
            'shipping_template' => 0,
            'introduction' => !empty($data['subTitle']) ? $data['subTitle'] : '',
            'keywords' =>!empty($data['intro']) ? $data['intro'] : '',
            'unit' => $smallPackageUnit, // 默认单位
            "sku_no" => $this->generateSkuNo($data, $data['spuId']),
            'sort' => 0,
            'video_url' => !empty($data['videos'][0]) ? $data['videos'][0] : '',
            'goods_service_ids' => '',
            'label_id' => $label_id,
            'label_name' => $label_name,
            'goods_attr_format' => '[]',
            'spec_type_status' => 0, // 默认单规格商品，后续判断
            'is_fenxiao' => 0,
            'fenxiao_type' => 1,
            'supplier_id' => 0,
            'brand_id' => !empty($data['brandId']) ? $data['brandId'] : 0,
            'brand_name' => '',
            'virtual_sale' => 0,
            'max_buy' => 0,
            'min_buy' => 0,
            'recommend_way' => 0,
            'is_limit' => 0,
            'limit_type' => 1,
            'sale_channel' => 'all',
            'sale_store' => 'all',
            'timer_on' => 0,
            'timer_off' => 0,
            'is_consume_discount' => 0
        ];

        // 处理多规格商品信息
        if (!empty($data['spuSpecInfo']) && is_array($data['spuSpecInfo']) && !empty($data['specInfo']) && is_array($data['specInfo'])) {
            // 设置为多规格商品
            $goodsData['spec_type_status'] = 1;
            
            // 处理规格项数据
            $specFormat = [];
            
            // 从specInfo中提取规格项
            foreach ($data['specInfo'] as $specGroup) {
                foreach ($specGroup as $specName => $specValues) {
                    $specItem = [
                        "spec_id" => -1,
                        "spec_name" => $specName,
                        "value" => []
                    ];
                    
                    // 添加规格值
                    foreach ($specValues as $index => $specValue) {
                        $specItem["value"][] = [
                            "spec_id" => -1,
                            "spec_name" => $specName,
                            "spec_value_id" => -($index + 1),
                            "spec_value_name" => $specValue,
                            "image" => ""
                        ];
                    }
                    
                    $specFormat[] = $specItem;
                }
            }
            
            // 处理SKU数据
            $skuData = [];
            $defaultFound = false;
            
            foreach ($data['spuSpecInfo'] as $index => $skuItem) {
                $specValuesList = [];
                $specName = "";
                
                // 构建规格值列表
                if (!empty($skuItem['specInfo']) && is_array($skuItem['specInfo'])) {
                    foreach ($skuItem['specInfo'] as $spec) {

                        if (!empty($spec['specKey']) && !empty($spec['specValue'])) {
                            $specName .= ($specName ? "," : "") . $spec['specValue'];
                            
                            // 查找匹配的规格值ID
                            $specValueId = -1;
                            foreach ($specFormat as $format) {
                                if ($format['spec_name'] == $spec['specKey']) {
                                    foreach ($format['value'] as $value) {
                                        if ($value['spec_value_name'] == $spec['specValue']) {
                                            $specValueId = $value['spec_value_id'];
                                            break;
                                        }
                                    }
                                }
                            }
                            
                            $specValuesList[] = [
                                "spec_id" => -1,
                                "spec_name" => $spec['specKey'],
                                "spec_value_id" => $specValueId,
                                "spec_value_name" => $spec['specValue'],
                                "image" => !empty($skuItem['thumbnailImage']) ? $skuItem['thumbnailImage'] : ""
                            ];
                        }
                    }
                }
                
                // 设置库存
                $skuStock = !empty($data['stockInfo']['stockQuantity']) ? intval($data['stockInfo']['stockQuantity']) : 10;
                $skuStockAlarm = !empty($data['stockInfo']['safeStockQuantity']) ? intval($data['stockInfo']['safeStockQuantity']) : 10;

                
                // 创建SKU项
                $skuDataItem = [
                    "sku_id" => !empty($skuItem['spuId']) ? $skuItem['spuId'] : "",
                    "spec_name" => $specName,
                    "sku_no" => $this->generateSkuNo($skuItem, $skuItem['spuId']) ,
                    "sku_spec_format" => $specValuesList,
                    "price" => $defaultPrice,
                    "market_price" => $defaultMarketPrice,
                    "cost_price" => $defaultCostPrice,
                    "stock" => $skuStock,
                    "stock_alarm" => $skuStockAlarm,
                    "sku_image" => !empty($skuItem['thumbnailImage']) ? $skuItem['thumbnailImage'] : $goodsData['goods_image'],
                    "sku_images" => !empty($skuItem['thumbnailImage']) ? $skuItem['thumbnailImage'] : $goodsData['goods_image'],
                    "sku_images_arr" => [],
                    "is_default" => $index === 0 || (!$defaultFound && isset($data['spuId']) && $skuItem['spuId'] === $data['spuId']) ? 1 : 0,
                    "weight" => $defaultWeight,
                    "volume" => 0
                ];
                
                // 设置默认SKU标记
                if ($skuDataItem['is_default'] === 1) {
                    $defaultFound = true;
                }
                
                $skuData[] = $skuDataItem;
            }
            
            // 如果没有找到默认SKU，将第一个SKU设为默认
            if (!$defaultFound && !empty($skuData)) {
                $skuData[0]['is_default'] = 1;
            }
            
            // 将规格数据转换为JSON字符串
            $goodsData['goods_spec_format'] = json_encode($specFormat, JSON_UNESCAPED_UNICODE);
            $goodsData['goods_sku_data'] = json_encode($skuData, JSON_UNESCAPED_UNICODE);
        }

        return $goodsData;
    }
    
    /**
     * 返回API响应数据
     * @param array $data 响应数据
     * @return \think\Response
     */
    protected function response($data)
    {
        if (!isset($data['timestamp'])) {
            $data['timestamp'] = time();
        }
        return Response::create($data, 'json', 200);
    }
} 